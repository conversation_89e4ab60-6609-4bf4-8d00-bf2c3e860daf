import { Page } from '@playwright/test';
import { getCurrentConfig } from '../config';
import { ApplicationFormParams, ApplicationSearchParams } from '../test_datas/ApplicationData';

const config = getCurrentConfig();

/**
 * 应用管理页面对象类
 */
export class ApplicationPage {
  /**
   * 构造函数
   * @param page Playwright页面对象
   */
  constructor(private page: Page) {}

  // 打开页面
  async open() {
    // 假设应用管理页面的URL路径是applicationManagement
    await this.page.goto(config.URLS.TCMSP_URL + 'applicationManagement');
  }

  // 新增应用
  async add(data: ApplicationFormParams) {
    await this.open();
    await this.page.getByText('新 增').click();
    await this.fillApplicationForm(data);
    await this.confirm();
  }

  // 确定按钮
  async confirm() {
    await this.page.getByRole('button', { name: '保 存' }).click();
  }

  // 取消按钮
  async cancel() {
    await this.page.getByRole('button', { name: '取 消' }).click();
  }

  // 编辑应用
  async edit(params: ApplicationSearchParams = {}, data: ApplicationFormParams) {
    await this.search(params);
    await this.page.getByText('编辑').first().click();
    await this.fillApplicationForm(data);
    await this.confirm();
  }

  // 清空搜索条件
  async clearSearch() {
    await this.page.getByRole('button', { name: '清空' }).click();
  }

  // 搜索应用
  async search(params: ApplicationSearchParams = {}) {
    await this.open();

    // 清空搜索条件
    await this.clearSearch();

    // 填写搜索条件
    if (params.applicationId) {
      await this.page.getByPlaceholder('应用ID').fill(params.applicationId);
    }

    if (params.applicationName) {
      await this.page.getByPlaceholder('应用名称').fill(params.applicationName);
    }

    if (params.shortName) {
      await this.page.getByPlaceholder('简称').fill(params.shortName);
    }

    if (params.category) {
      // 点击应用分类下拉框
      await this.page.locator('.select-drop-down .below').click();
      // 等待下拉菜单出现
      await this.page.waitForTimeout(500);
      // 选择应用分类
      await this.page.getByRole('listitem').filter({ hasText: params.category }).click();

    }

    if (params.remark) {
      await this.page.getByPlaceholder('备注').fill(params.remark);
    }

    // 点击查询按钮
    await this.page.getByRole('button', { name: '查询' }).click();
  }

  // 填写应用表单
  async fillApplicationForm(data: ApplicationFormParams) {
    try {
      // 填写基本信息
      if (data.applicationName) {
        // 使用文本内容定位标签，然后找到相邻的输入框
        await this.page.locator('.search-body-col:has-text("应用名称") input').fill(data.applicationName);
      }

      if (data.shortName) {
        await this.page.locator('.search-body-col:has-text("简称") input').fill(data.shortName);
      }

      if (data.category) {
        // 点击应用分类选择框
        await this.page.locator('.search-body-col:has-text("应用分类") .select-drop-down .below').click();
        // 等待下拉菜单出现
        await this.page.waitForTimeout(500);
        // 选择应用分类
        await this.page.getByRole('listitem').filter({ hasText: data.category }).click();

      }

      if (data.clientId) {
        await this.page.locator('.search-body-col:has-text("ClientID") input').fill(data.clientId);
      }

      if (data.domain) {
        await this.page.locator('.search-body-col:has-text("网址域名") input').fill(data.domain);
      }

      if (data.channel) {
        // 点击关联渠道下拉框
        await this.page.locator('.search-body-col:has-text("关联渠道") .select-drop-down .below').click();

        // 等待下拉菜单出现
        await this.page.waitForSelector('select-dropdown .options', { state: 'visible', timeout: 5000 });

        // 直接选择指定的渠道
        await this.page.locator(`select-dropdown .options li span:text-is("${data.channel}")`).click().catch(async e => {
          console.error(`未找到名为 "${data.channel}" 的渠道选项`, e);

          // 获取所有可用的渠道选项，用于调试
          const channelOptions = await this.page.locator('select-dropdown .options li span').allTextContents();
          console.log('可用的渠道选项:', channelOptions);
        });

        // 等待下拉菜单关闭
        await this.page.waitForTimeout(500);
      }

      if (data.remark) {
        // 备注是textarea元素
        await this.page.locator('.search-body-col:has-text("备注") textarea').fill(data.remark);
      }
    } catch (error) {
      console.error('填写表单失败:', error);
      // 添加截图帮助调试
      await this.page.screenshot({ path: `form-error-${Date.now()}.png` });
      throw error;
    }
  }

  // 查看应用详情
  async viewDetail(params: ApplicationSearchParams = {}) {
    await this.search(params);
    await this.page.getByText('详情').first().click();
    // 等待详情页面加载
    await this.page.waitForSelector('app-application-management-detail');
  }

  // 删除应用
  async delete(params: ApplicationSearchParams = {}) {
    await this.search(params);
    await this.page.getByText('删除').first().click();
    // 等待确认对话框
    await this.page.getByText('您确定要删除选中的数据吗？').waitFor();
    // 点击确认按钮
    await this.page.getByRole('button', { name: '确定' }).click();
  }

  /**
   * 根据列名获取列索引
   * @param columnName 列名
   * @returns 列索引（从1开始）
   */
  async getColumnIndexByName(columnName: string): Promise<number> {
    // 等待表格头部加载完成
    await this.page.waitForSelector('.datatable-header-cell-label', { state: 'visible', timeout: 10000 });

    // 等待特定列名出现
    try {
      await this.page.waitForFunction(
        (colName) => {
          const headers = document.querySelectorAll('.datatable-header-cell-label');
          return Array.from(headers).some(header => header.textContent?.trim() === colName);
        },
        columnName,
        { timeout: 10000 }
      );
    } catch (error) {
      console.error(`等待列名 "${columnName}" 出现超时`);
      // 继续执行，让下面的代码尝试查找列
    }

    const headers = this.page.locator('.datatable-header-cell-label');
    const count = await headers.count();

    if (count === 0) {
      throw new Error('表格头部未加载或未找到任何列');
    }

    for (let i = 0; i < count; i++) {
      const text = await headers.nth(i).textContent();
      if (text?.trim() === columnName) {
        return i + 1; // 返回1-based索引
      }
    }

    // 如果没有找到匹配的列名，记录所有可用的列名以便调试
    const availableColumns: string[] = [];
    for (let i = 0; i < count; i++) {
      const text = await headers.nth(i).textContent();
      if (text) {
        availableColumns.push(text.trim());
      }
    }

    throw new Error(`未找到列名为 "${columnName}" 的列。可用的列名有: ${availableColumns.join(', ')}`);
  }

  /**
   * 通用方法：获取指定行和列的单元格值
   * @param columnName 列名，如"应用ID"、"应用名称"等
   * @param rowIndex 行索引，从0开始，默认为0（第一行）
   * @returns 单元格的文本内容
   */
  async getCellValue(columnName: string, rowIndex: number = 0): Promise<string> {
    // 等待表格体加载完成
    await this.page.waitForSelector('.datatable-row-center.datatable-row-group', { state: 'visible', timeout: 10000 });

    // 获取列索引
    const columnIndex = await this.getColumnIndexByName(columnName);

    // 获取所有行
    const rows = this.page.locator('.datatable-row-center.datatable-row-group');
    const rowCount = await rows.count();

    if (rowCount === 0) {
      throw new Error('表格数据未加载或没有数据行');
    }

    if (rowIndex >= rowCount) {
      throw new Error(`行索引超出范围：请求的行索引为 ${rowIndex}，但总行数为 ${rowCount}`);
    }

    // 获取指定行
    const row = rows.nth(rowIndex);

    // 等待该行中的单元格加载完成
    await this.page.waitForSelector(`.datatable-row-center.datatable-row-group >> nth=${rowIndex} >> datatable-body-cell`,
      { state: 'visible', timeout: 5000 });

    // 获取单元格
    const cell = row.locator(`datatable-body-cell:nth-child(${columnIndex}) .datatable-body-cell-label`);

    try {
      // 等待单元格内容加载
      await cell.waitFor({ state: 'visible', timeout: 5000 });

      // 直接获取单元格文本内容，不需要特殊处理
      return (await cell.textContent() || '').trim();
    } catch (error) {
      console.error(`获取 ${columnName} 列的值时出错:`, error);
      // 尝试直接获取单元格的innerHTML作为备选方案
      const innerHTML = await cell.evaluate(node => node.innerHTML);
      return innerHTML.replace(/<[^>]*>/g, '').trim();
    }
  }

  // 实现locator方法
  locator(selector: string) {
    return this.page.locator(selector);
  }

  // 实现getByRole方法
  getByRole(role: 'alert' | 'alertdialog' | 'application' | 'article' | 'banner' | 'blockquote' | 'button' | 'caption' | 'cell' | 'checkbox' | 'code' | 'columnheader' | 'combobox' | 'complementary' | 'contentinfo' | 'definition' | 'deletion' | 'dialog' | 'directory' | 'document' | 'emphasis' | 'feed' | 'figure' | 'form' | 'generic' | 'grid' | 'gridcell' | 'group' | 'heading' | 'img' | 'insertion' | 'link' | 'list' | 'listbox' | 'listitem' | 'log' | 'main' | 'marquee' | 'math' | 'meter' | 'menu' | 'menubar' | 'menuitem' | 'menuitemcheckbox' | 'menuitemradio' | 'navigation' | 'none' | 'note' | 'option' | 'paragraph' | 'presentation' | 'progressbar' | 'radio' | 'radiogroup' | 'region' | 'row' | 'rowgroup' | 'rowheader' | 'scrollbar' | 'search' | 'searchbox' | 'separator' | 'slider' | 'spinbutton' | 'status' | 'strong' | 'subscript' | 'superscript' | 'switch' | 'tab' | 'table' | 'tablist' | 'tabpanel' | 'term' | 'textbox' | 'time' | 'timer' | 'toolbar' | 'tooltip' | 'tree' | 'treegrid' | 'treeitem', options?: { name?: string | RegExp, exact?: boolean }) {
    return this.page.getByRole(role, options);
  }

  // 实现getByText方法
  getByText(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByText(text, options);
  }

  // 实现getByPlaceholder方法
  getByPlaceholder(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByPlaceholder(text, options);
  }
}

