// 环境类型
export type EnvironmentType = 'FAT' | 'UAT' | 'PROD';

// 默认环境
export const DEFAULT_ENV: EnvironmentType = 'FAT';

// 当前环境
export const CURRENT_ENV: EnvironmentType = (process.env.ENVIRONMENT as EnvironmentType) || DEFAULT_ENV;

// 系统URL配置
export const URLS = {
  FAT: {
    TCMSP_URL: 'http://tcmsp-web.lunztech.cn/',
  },
  UAT: {
    TCMSP_URL: '', 
  },
  PROD: {
    TCMSP_URL: ''
  }
};

// 账号配置
export const ACCOUNTS = {
  FAT: {
    ADMIN_USERNAME: 'rlhxsuperadmin',
    ADMIN_PASSWORD: 'Rlhxsuperadmin',
    DOCTOR_USERNAME: 'rzys01',
    DOCTOR_PASSWORD: 'Rzys01',
    ORGNIZATION_USERNAME: 'rzliu',
    ORGNIZATION_PASSWORD: 'Rzliu'
  },
  UAT: {
    ADMIN_USERNAME: '',
    ADMIN_PASSWORD: '',
    DOCTOR_USERNAME: '',
    DOCTOR_PASSWORD: '',
    ORGNIZATION_USERNAME: '',
    ORGNIZATION_PASSWORD: ''
  },
  PROD: {
    ADMIN_USERNAME: '',
    ADMIN_PASSWORD: '',
    DOCTOR_USERNAME: '',
    DOCTOR_PASSWORD: '',
    ORGNIZATION_USERNAME: '',
    ORGNIZATION_PASSWORD: ''
  }
};

// 业务配置
export const BUSINESS = {
  FAT: {
    ORGNIZATION_NAME: '瑞泽医疗'
  },
  UAT: {

  },
  PROD: {

  }
};

// 存储配置 无需更改
export const STORAGE = {
  ADMIN_STORAGE: 'auth-data/admin.json',// 管理员登录信息存储路径
  DOCTOR_STORAGE: 'auth-data/doctor.json',// 医生登录信息存储路径
  ORGNIZATION_STORAGE: 'auth-data/orgnization.json',// 机构登录信息存储路径
};

// 常量配置
export const CONSTANTS = {
  ORDER_STATUS: {
    PENDING: '待处理',
    PROCESSING: '处理中',
    COMPLETED: '已完成'
  },
  MAX_RETRY_COUNT: 3,
  DEFAULT_TIMEOUT: 30000
};

// 获取当前环境的配置 无需更改
export const getCurrentConfig = () => ({
  URLS: URLS[CURRENT_ENV],
  ACCOUNTS: ACCOUNTS[CURRENT_ENV],
  BUSINESS: BUSINESS[CURRENT_ENV],
  STORAGE: STORAGE,
  CONSTANTS: CONSTANTS
});