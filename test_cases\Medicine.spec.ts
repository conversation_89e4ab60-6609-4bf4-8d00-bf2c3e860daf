import { test, expect } from '@playwright/test';
import { MedicinePage } from '../test_pages/MedicinePage';
import { getCurrentConfig } from '../config';
import { BrowserManager } from '../utils/utils';
import * as testData from '../test_datas/MedicineData';

const config = getCurrentConfig();

// 配置测试用例为串行执行，确保测试用例按顺序执行
test.describe.configure({ mode: 'serial' });

test.describe('药品管理', () => {
  // 定义全局变量
  let medicinePage: MedicinePage;
  let browserManager: BrowserManager;
  let medicineNo: string;

  test.beforeAll(async ({ browser }) => {
    // 初始化浏览器管理器
    browserManager = BrowserManager.getInstance();
    // 创建药品管理页面实例，使用医师权限
    medicinePage = await browserManager.createPage(browser, config.STORAGE.DOCTOR_STORAGE, MedicinePage);
  });

  test.describe('ZYWZ-000 药品基本操作', () => {
    /**
     * 测试用例：空数据保存
     * 步骤：
     * 1. 打开药品管理页面
     * 2. 点击新增药品按钮
     * 3. 不填写任何数据
     * 4. 点击保存按钮
     * 5. 验证错误提示
     * 6. 取消操作
     */
    test('空数据保存', async () => {
      // 执行新增药品操作
      await medicinePage.add(testData.emptyData);
      // 验证错误提示
      await expect(medicinePage.getByRole('alertdialog')).toContainText('信息填写不完整，请检查');
      await medicinePage.cancel();
    });

    /**
     * 测试用例：添加必填字段药品
     * 步骤：
     * 1. 打开药品管理页面
     * 2. 点击新增药品按钮
     * 3. 只填写必填字段
     * 4. 点击保存按钮
     * 5. 搜索并验证药品信息
     * 6. 获取药品编号用于后续测试
     */
    test('添加必填字段药品', async () => {
      // 添加只包含必填字段的药品
      await medicinePage.add(testData.requiredFieldsData);

      // 搜索并验证药品信息
      await medicinePage.search({ medicineName: testData.requiredFieldsData.name });

      // 验证药品已添加
      await expect(medicinePage.locator('datatable-body-row')).toBeVisible();
      await expect(medicinePage.locator('datatable-body-row')).toContainText(testData.requiredFieldsData.name);
      await expect(medicinePage.locator('datatable-body-row')).toContainText(testData.requiredFieldsData.type);

      // 获取药品编号
      medicineNo = await medicinePage.getCellValue('药品编号');
    });

    /**
     * 测试用例：添加中草药
     * 步骤：
     * 1. 打开药品管理页面
     * 2. 点击新增药品按钮
     * 3. 填写中草药信息
     * 4. 点击保存按钮
     * 5. 搜索并验证药品信息
     */
    test('添加中草药', async () => {
      // 添加中草药
      await medicinePage.add(testData.herbMedicineData);

      // 搜索并验证药品信息
      await medicinePage.search({ medicineName: testData.herbMedicineData.name });

      // 验证药品已添加
      await expect(medicinePage.locator('datatable-body-row')).toBeVisible();
      await expect(medicinePage.locator('datatable-body-row')).toContainText(testData.herbMedicineData.name);
      await expect(medicinePage.locator('datatable-body-row')).toContainText(testData.herbMedicineData.type);
    });

    /**
     * 测试用例：添加中成药
     * 步骤：
     * 1. 打开药品管理页面
     * 2. 点击新增药品按钮
     * 3. 填写中成药信息
     * 4. 点击保存按钮
     * 5. 搜索并验证药品信息
     */
    test('添加中成药', async () => {
      // 添加中成药
      await medicinePage.add(testData.patentMedicineData);

      // 搜索并验证药品信息
      await medicinePage.search({ medicineName: testData.patentMedicineData.name });

      // 验证药品已添加
      await expect(medicinePage.locator('datatable-body-row')).toBeVisible();
      await expect(medicinePage.locator('datatable-body-row')).toContainText(testData.patentMedicineData.name);
      await expect(medicinePage.locator('datatable-body-row')).toContainText(testData.patentMedicineData.type);
    });



    /**
     * 测试用例：编辑药品
     * 步骤：
     * 1. 打开药品管理页面
     * 2. 搜索要编辑的药品
     * 3. 点击编辑按钮
     * 4. 修改药品信息
     * 5. 点击保存按钮
     * 6. 搜索并验证药品信息
     */
    test('编辑药品', async () => {
      // 编辑药品
      await medicinePage.edit({ medicineName: testData.herbMedicineData.name }, testData.editHerbMedicineData);

      // 搜索并验证药品信息
      await medicinePage.search({ medicineName: testData.editHerbMedicineData.name });

      // 验证药品已编辑
      await expect(medicinePage.locator('datatable-body-row')).toBeVisible();
      await expect(medicinePage.locator('datatable-body-row')).toContainText(testData.editHerbMedicineData.name);
      await expect(medicinePage.locator('datatable-body-row')).toContainText(testData.editHerbMedicineData.type);
      await expect(medicinePage.locator('datatable-body-row')).toContainText(testData.editHerbMedicineData.retailPrice);
      await expect(medicinePage.locator('datatable-body-row')).toContainText(testData.editHerbMedicineData.specification || '');
    });

    /**
     * 测试用例：下架药品
     * 步骤：
     * 1. 打开药品管理页面
     * 2. 搜索要下架的药品
     * 3. 点击下架按钮
     * 4. 确认下架操作
     * 5. 验证药品状态已变更为下架
     */
    test('下架药品', async () => {
      // 下架药品
      await medicinePage.unshelve({ medicineName: testData.patentMedicineData.name });

      // 验证药品已下架
      await expect(medicinePage.locator('datatable-body-row')).toContainText('下架');
    });



    /**
     * 测试用例：验证搜索功能
     * 步骤：
     * 1. 打开药品管理页面
     * 2. 使用多个条件进行组合搜索（药品编号、药品名称、创建日期）
     * 3. 点击查询按钮
     * 4. 验证搜索结果是否符合预期
     * 5. 清空搜索条件
     */
    test('验证搜索功能', async () => {
      // 使用综合条件进行搜索
      await medicinePage.search({
        medicineNo: medicineNo.trim(),
        medicineName: testData.requiredFieldsData.name,
        createDateRange: testData.searchTestData.createDateRange
      });

      // 验证搜索结果
      await expect(medicinePage.locator('datatable-body-row')).toBeVisible();
      await expect(medicinePage.locator('datatable-body-row')).toContainText(medicineNo.trim());
      await expect(medicinePage.locator('datatable-body-row')).toContainText(testData.requiredFieldsData.name);

      // 清空搜索条件
      await medicinePage.clearSearch();
    });

    /**
     * 测试用例：删除药品
     * 步骤：
     * 1. 打开药品管理页面
     * 2. 搜索要删除的药品
     * 3. 点击删除按钮
     * 4. 确认删除操作
     * 5. 搜索并验证药品已删除
     * 6. 重复上述步骤删除所有测试药品
     */
    test('删除药品', async () => {
      // 删除药品
      await medicinePage.delete({ medicineName: testData.patentMedicineData.name });
      await medicinePage.delete({ medicineName: testData.editHerbMedicineData.name });
      await medicinePage.delete({ medicineName: testData.requiredFieldsData.name });

      // 搜索并验证药品已删除
      await medicinePage.search({ medicineName: testData.patentMedicineData.name });
      await expect(medicinePage.locator('datatable-body-row')).not.toBeVisible();
      await medicinePage.search({ medicineName: testData.editHerbMedicineData.name });
      await expect(medicinePage.locator('datatable-body-row')).not.toBeVisible();
      await medicinePage.search({ medicineName: testData.requiredFieldsData.name });
      await expect(medicinePage.locator('datatable-body-row')).not.toBeVisible();
    });
  });
});
