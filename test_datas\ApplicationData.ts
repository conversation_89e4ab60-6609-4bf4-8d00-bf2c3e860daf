/**
 * 应用表单参数接口
 */
export interface ApplicationFormParams {
  /** 应用ID，系统自动生成，不需要填写 */
  applicationId?: string;
  /** 应用名称，必填 */
  applicationName: string;
  /** 简称，非必填 */
  shortName?: string;
  /** 应用图标，非必填 */
  icon?: string;
  /** 应用分类，非必填 */
  category?: string;
  /** ClientID，必填 */
  clientId: string;
  /** 网址域名，非必填 */
  domain?: string;
  /** 关联渠道，必填，默认为"同名渠道" */
  channel: string;
  /** 备注，非必填 */
  remark?: string;
}

/**
 * 应用搜索参数接口
 */
export interface ApplicationSearchParams {
  /** 应用ID */
  applicationId?: string;
  /** 应用名称 */
  applicationName?: string;
  /** 简称 */
  shortName?: string;
  /** 应用分类 */
  category?: string;
  /** 备注 */
  remark?: string;
}

// 空数据
export const emptyData: ApplicationFormParams = {
  applicationName: '',
  shortName: '',
  category: '',
  clientId: '',
  domain: '',
  channel: '同名渠道',
  remark: ''
};

// 基本应用测试数据
export const basicApplicationData: ApplicationFormParams = {
  applicationName: `基础应用`,
  shortName: `基础`,
  category: 'web应用',
  clientId: `client`,
  domain: `https://example.com`,
  channel: '中医版ERP',
  remark: '这是一个基础应用测试数据'
};

// 编辑应用测试数据
export const editApplicationData: ApplicationFormParams = {
  applicationName: `编辑后应用`,
  shortName: `编辑`,
  category: '小程序',
  clientId: `client_edit`,
  domain: `https://edit-example.com`,
  channel: '商城小程序', // 从下拉列表中选择其他渠道
  remark: '这是编辑后的应用测试数据'
};

// 必填字段测试数据
export const requiredFieldsData: ApplicationFormParams = {
  applicationName: `必填应用`,
  clientId: `client_required`,
  channel: '诊所移动端'
};

// 搜索测试数据
export const searchTestData: ApplicationSearchParams = {
  applicationId: '', // 将在测试用例中从列表第一行获取
  applicationName: '', // 将在测试用例中从列表第一行获取
  shortName: '',
  category: '',
  remark: ''
};