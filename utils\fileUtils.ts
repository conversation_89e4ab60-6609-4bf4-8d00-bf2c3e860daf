import { Page } from '@playwright/test';

/**
 * 文件操作工具类
 * 提供文件上传和等待相关的功能
 */

/**
 * 上传文件并等待上传完成
 * 专门用于笑唯美医生端的文件上传功能
 * 
 * @param page 页面实例
 * @param index 上传索引，用于定位多个上传组件中的特定一个
 * 
 * @example
 * // 上传第一个文件
 * await uploadAndWait(page, 0);
 * 
 * @description
 * 该函数会：
 * 1. 选择指定索引的文件上传组件
 * 2. 上传预设的测试图片
 * 3. 等待上传进度条消失
 * 4. 等待图片URL更新为阿里云OSS地址
 */
export async function uploadAndWait(page: Page, index: number) {
  // 上传文件
  await page.locator('.ivu-upload-input').nth(index).setInputFiles('test-data\\ceph.png');

  // 等待当前上传完成
  await page.waitForFunction((i) => {
    // 获取上传进度条元素
    const progressBar = document.querySelectorAll('.photo-list li .xwm-upload-progress')[i] as HTMLElement;
    // 获取上传后的图片元素
    const currentImg = document.querySelectorAll('.photo-list li .img-box img.faceimg')[i] as HTMLImageElement;
    
    // 检查上传是否完成
    return progressBar &&
      progressBar.style.display === 'none' && // 进度条消失
      currentImg &&
      currentImg.src &&
      currentImg.src.includes('https://xwmqd.oss-cn-qingdao.aliyuncs.com'); // 图片URL已更新为OSS地址
  }, index);
} 