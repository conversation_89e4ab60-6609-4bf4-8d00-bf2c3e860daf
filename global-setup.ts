import { chromium, FullConfig } from '@playwright/test';
import { getCurrentConfig } from './config';

const config = getCurrentConfig();

/**
 * 通用登录方法
 * @param username 用户名
 * @param password 密码
 * @param storagePath 登录状态存储路径
 * @param roleName 角色名称（用于日志输出）
 * @returns 登录成功返回 true，失败返回 false
 */
async function login(username: string, password: string, storagePath: string, roleName: string): Promise<boolean> {
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // 访问登录页
    await page.goto(config.URLS.TCMSP_URL);

    // 输入用户名和密码
    await page.getByRole('textbox', { name: '请输入帐号' }).click();
    await page.getByRole('textbox', { name: '请输入帐号' }).fill(username);
    await page.getByRole('textbox', { name: '请输入密码' }).click();
    await page.getByRole('textbox', { name: '请输入密码' }).fill(password);

    // 点击登录按钮
    await page.getByRole('button', { name: '登录' }).click();

    // 等待登录成功
    await page.waitForURL('**/dashboard', { timeout: 30000 });

    // 保存登录状态到指定路径
    await context.storageState({ path: storagePath });
    console.log(`${roleName}登录成功`);
    return true;
  } catch (error) {
    console.error(`${roleName}登录失败:`, error);
    return false;
  } finally {
    await page.close();
    await context.close();
    await browser.close();
  }
}



// 导出默认函数
export default async function globalSetup(fullConfig: FullConfig) {
  // 并行执行登录操作
  await Promise.all([
    // 管理员登录
    login(
      config.ACCOUNTS.ADMIN_USERNAME,
      config.ACCOUNTS.ADMIN_PASSWORD,
      config.STORAGE.ADMIN_STORAGE,
      '管理员'
    ),
    // 医生登录
    login(
      config.ACCOUNTS.DOCTOR_USERNAME,
      config.ACCOUNTS.DOCTOR_PASSWORD,
      config.STORAGE.DOCTOR_STORAGE,
      '医生'
    ),
    // 机构登录
    login(
      config.ACCOUNTS.ORGNIZATION_USERNAME,
      config.ACCOUNTS.ORGNIZATION_PASSWORD,
      config.STORAGE.ORGNIZATION_STORAGE,
      '机构'
    )
  ]);
}
