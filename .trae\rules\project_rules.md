


          
首先需要分析项目的代码结构和测试用例编写规范，以便制定合适的chat规则。
        
            
toolName: view_files
            
status: success
          
            
filePath: d:/doc/playwright-tcmsp/README.md
          
继续查看测试用例编写规范部分，以了解更多项目规范细节。
        
            
toolName: view_files
            
status: success
          
            
filePath: d:/doc/playwright-tcmsp/README.md
          
基于对项目代码的分析，以下是针对该Playwright TCMSP自动化测试框架的专属chat规则：

1. 项目结构规范
- 严格遵循三层架构：测试用例（test_cases）、页面对象（test_pages）和测试数据（test_datas）
- 文件命名采用PascalCase命名法：
  - 测试用例文件：`*.spec.ts`
  - 页面对象文件：`*Page.ts`
  - 测试数据文件：`*Data.ts`

2. 代码编写规范
- 页面对象（POM）规范：
  - 必须实现基础方法：`open()`, `add()`, `edit()`, `delete()`, `search()`, `confirm()`, `cancel()`, `close()`
  - 元素定位优先级：
    1. 语义化定位（getByRole/getByText）
    2. CSS选择器
    3. 避免XPath

3. 测试数据管理
- 使用TypeScript接口定义数据结构
- 必须为字段添加注释，标明必填/选填
- 使用时间戳创建唯一测试数据
- 标准测试数据集：
  - 空数据（emptyData）
  - 必填字段数据（requiredFieldsData）
  - 完整字段数据（completeData）
  - 编辑测试数据（editData）

4. 测试用例编写
- 测试用例执行顺序：
  1. 空数据保存测试
  2. 必填字段测试
  3. 完整字段测试
  4. 编辑和删除测试
- 每个测试用例必须包含详细步骤注释
- 使用`test.describe.configure({ mode: 'serial' })`确保顺序执行

5. 浏览器管理
- 使用BrowserManager单例模式管理浏览器实例
- 根据不同权限使用对应的存储状态文件

6. 环境配置
- 支持多环境（FAT/UAT/PROD）配置
- 通过环境变量`ENVIRONMENT`控制运行环境

7. 断言规范
- 使用Playwright的expect进行断言
- 验证UI元素可见性和内容正确性
- 对错误提示进行准确断言

8. 注释规范
- 测试用例必须包含用例名称和详细步骤说明
- 接口定义中必须注明字段是否必填
- 关键方法需要添加功能说明注释

这些规则将帮助确保测试代码的一致性、可维护性和可靠性。
        