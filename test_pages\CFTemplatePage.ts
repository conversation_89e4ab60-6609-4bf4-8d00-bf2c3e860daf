import { Page } from '@playwright/test';
import { getCurrentConfig } from '../config';
import { FormParams, SearchParams } from '../test_datas/CFTemplateData';

const config = getCurrentConfig();

export class CFTemplatePage {
  constructor(private page: Page) { }

  // 打开页面
  async open() {
    await this.page.goto(config.URLS.TCMSP_URL + 'templateManagement');
    await this.page.getByText('处方模板', { exact: true }).click();
  }

  // 新增模板
  async add(data: FormParams) {
    await this.open();
    await this.page.click('button:has-text("新增模板")');
    await this.fillForm(data);
    await this.confirm();
  }

  // 确定按钮
  async confirm() {
    await this.page.click('button:has-text("确 定")');
  }

  // 取消按钮
  async cancel() {
    await this.page.click('button:has-text("取 消")');
  }

  // 关闭按钮
  async close() {
    await this.page.click('button:has-text("关 闭")');
  }

  // 编辑按钮
  async edit(params: SearchParams = {}, data: FormParams) {
    await this.search(params);
    await this.page.locator('.datatable-body-row:first-child .operation-a_hover:has-text("编辑")').click();
    await this.fillForm(data);
    await this.confirm();
  }

  // 详情按钮
  async detail(params: SearchParams = {}) {
    await this.search(params);
    await this.page.locator('.datatable-body-row:first-child .operation-a_hover:has-text("详情")').click();
  }

  // 清空按钮
  async clearSearch() {
    await this.page.click('button.btn-reset');
  }

  // 列表查询
  async search(params: SearchParams = {}) {
    await this.open();

    // 判断并点击展开按钮
    const expandButton = this.page.locator('label:has-text("展开")');
    const isVisible = await expandButton.isVisible();
    if (isVisible) {
      await expandButton.click();
    }

    // 填写查询条件
    if (params.templateNo) {
      await this.page.locator('input[placeholder="模板编号"]').fill(params.templateNo);
    }
    if (params.name) {
      await this.page.locator('input[placeholder="模板名称"]').fill(params.name);
    }
    if (params.type) {
      await this.page.locator('label:has-text("模板分类") + div').click();
      await this.page.locator('label:has-text("模板分类") + div input').fill(params.type);
      await this.page.locator(`.options li span:has-text("${params.type}")`).click();
    }
    if (params.diagnosis) {
      await this.page.locator('input[placeholder="诊断"]').fill(params.diagnosis);
    }
    if (params.description) {
      await this.page.locator('input[placeholder="说明"]').fill(params.description);
    }
    if (params.creator) {
      await this.page.locator('input[placeholder="创建人"]').fill(params.creator);
    }
    if (params.createTimeRange) {
      await this.page.locator('label:has-text("创建时间") + div').click();
      // 等待日期面板显示
      await this.page.waitForSelector('.ant-calendar-date-panel');
      // 使用更精确的选择器定位日期输入框
      const startDateInput = this.page.locator('.ant-calendar-range-left .ant-calendar-input-wrap input[placeholder="开始日期"]');
      const endDateInput = this.page.locator('.ant-calendar-range-right .ant-calendar-input-wrap input[placeholder="结束日期"]');

      // 点击并输入开始日期
      await startDateInput.click();
      for (const char of params.createTimeRange.start) {
        await this.page.keyboard.press(char);
      }

      // 点击并输入结束日期
      await endDateInput.click();
      for (const char of params.createTimeRange.end) {
        await this.page.keyboard.press(char);
      }
    }
    // 点击查询按钮
    await this.page.click('button.btn-search');
  }

  // 填写模板表单
  async fillForm(data: FormParams) {
    try {
      // 填写模板信息
      await this.page.locator('.prescription-item .medication-title:has-text("模板名称") + textarea').fill(data.name);

      // 选择处方类型
      if (data.type === '中草药处方') {
        await this.page.locator('.m-radio:has-text("中草药处方")').click();
      } else if (data.type === '中成药处方') {
        await this.page.locator('.m-radio:has-text("中成药处方")').click();
      }

      // 填写诊断和说明
      await this.page.locator('.prescription-item .medication-title:has-text("诊断") + textarea').fill(data.diagnosis);
      if (data.description) {
        await this.page.locator('.prescription-item .medication-title:has-text("说明") + textarea').fill(data.description);
      }

      // 处理药品列表
      if ('medicines' in data) {
        // 先删除现有的药品行（如果有）
        const existingRows = await this.page.locator('tbody tr').count();
        if (existingRows > 0) {
          console.log(`删除${existingRows}行现有药品`);
          for (let i = 0; i < existingRows; i++) {
            await this.page.locator('tbody tr:first-child .close-icon').click();
            // 等待一下，确保删除操作完成
            await this.page.waitForTimeout(300);
          }
        }

        // 添加新的药品
        if (data.medicines && data.medicines.length > 0) {
          for (const medicine of data.medicines) {
            // 点击添加药品输入框
            const inputField = this.page.locator('.prescription-table-add input[placeholder="请输入药品名称"]');
            await inputField.click();

            // 使用type方法模拟用户输入，而不是直接fill
            await inputField.type(medicine.name, { delay: 100 });

            // 等待一下，确保搜索触发
            await this.page.waitForTimeout(500);

            // 等待下拉列表出现
            await this.page.waitForSelector('.choose-info-modal', { timeout: 5000 });
            await this.page.locator('.choose-info-modal tbody tr:has-text("' + medicine.name + '")').click();

            // 等待药品添加到表格中
            await this.page.waitForSelector(`tbody tr:has-text("${medicine.name}")`);

            // 填写药品数量
            await this.page.locator('tbody tr:last-child .table-input').fill(medicine.quantity);
            // 注意：单位字段不可编辑，系统自动显示
          }
        }

        // 填写用药说明
        if (data.decoction) {
          await this.page.locator('.medication-title:has-text("煎煮方法") + textarea').fill(data.decoction);
        }

        if (data.totalDosage) {
          await this.page.locator('.medication-title:has-text("总贴数") + textarea').fill(data.totalDosage);
        }

        if (data.usage) {
          await this.page.locator('.medication-title:has-text("用药方法") + textarea').fill(data.usage);
        }

        if (data.frequency) {
          await this.page.locator('.medication-title:has-text("用药频次") + textarea').fill(data.frequency);
        }

        if (data.remark) {
          await this.page.locator('.medication-title:has-text("备注") + textarea').fill(data.remark);
        }
      }
    } catch (error) {
      console.error('填写表单失败:', error);
      // 添加截图帮助调试
      await this.page.screenshot({ path: `form-error-${Date.now()}.png` });
      throw error; // 重新抛出错误，保持原有行为
    }
  }

  // 实现locator方法
  locator(selector: string) {
    return this.page.locator(selector);
  }

  // 实现getByRole方法
  getByRole(role: 'alert' | 'alertdialog' | 'application' | 'article' | 'banner' | 'blockquote' | 'button' | 'caption' | 'cell' | 'checkbox' | 'code' | 'columnheader' | 'combobox' | 'complementary' | 'contentinfo' | 'definition' | 'deletion' | 'dialog' | 'directory' | 'document' | 'emphasis' | 'feed' | 'figure' | 'form' | 'generic' | 'grid' | 'gridcell' | 'group' | 'heading' | 'img' | 'insertion' | 'link' | 'list' | 'listbox' | 'listitem' | 'log' | 'main' | 'marquee' | 'math' | 'meter' | 'menu' | 'menubar' | 'menuitem' | 'menuitemcheckbox' | 'menuitemradio' | 'navigation' | 'none' | 'note' | 'option' | 'paragraph' | 'presentation' | 'progressbar' | 'radio' | 'radiogroup' | 'region' | 'row' | 'rowgroup' | 'rowheader' | 'scrollbar' | 'search' | 'searchbox' | 'separator' | 'slider' | 'spinbutton' | 'status' | 'strong' | 'subscript' | 'superscript' | 'switch' | 'tab' | 'table' | 'tablist' | 'tabpanel' | 'term' | 'textbox' | 'time' | 'timer' | 'toolbar' | 'tooltip' | 'tree' | 'treegrid' | 'treeitem', options?: { name?: string | RegExp, exact?: boolean }) {
    return this.page.getByRole(role, options);
  }

  // 实现getByText方法
  getByText(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByText(text, options);
  }

  // 实现getByPlaceholder方法
  getByPlaceholder(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByPlaceholder(text, options);
  }

  // 实现getByLabel方法
  getByLabel(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByLabel(text, options);
  }
}
