import { test, expect } from '@playwright/test';
import { ApplicationPage } from '../test_pages/ApplicationPage';
import { getCurrentConfig } from '../config';
import { BrowserManager } from '../utils/utils';
import * as testData from '../test_datas/ApplicationData';

const config = getCurrentConfig();

// 配置测试用例为串行执行，确保测试用例按顺序执行
test.describe.configure({ mode: 'serial' });

test.describe('应用管理', () => {
  // 定义全局变量
  let applicationPage: ApplicationPage;
  let browserManager: BrowserManager;
  let applicationId: string;

  test.beforeAll(async ({ browser }) => {
    // 初始化浏览器管理器
    browserManager = BrowserManager.getInstance();
    // 创建应用管理页面实例，使用管理员权限
    applicationPage = await browserManager.createPage(browser, config.STORAGE.ADMIN_STORAGE, ApplicationPage);
  });

  test.describe('ZYWZ-050 应用基本操作', () => {

    /**
     * 测试用例：空数据保存
     * 步骤：
     * 1. 打开应用管理页面
     * 2. 点击新增按钮
     * 3. 不填写任何数据
     * 4. 点击保存按钮
     * 5. 验证错误提示
     * 6. 取消操作
     */
    test('空数据保存', async () => {
      // 执行新增应用操作
      await applicationPage.add(testData.emptyData);
      // 验证错误提示
      await expect(applicationPage.getByRole('alertdialog')).toContainText('信息填写不完整，请检查');
      await applicationPage.cancel();
    });

    /**
     * 测试用例：添加必填字段应用
     * 步骤：
     * 1. 打开应用管理页面
     * 2. 点击新增按钮
     * 3. 只填写必填字段（应用名称、ClientID、关联渠道）
     * 4. 点击保存按钮
     * 5. 搜索并获取应用ID
     * 6. 查看应用详情
     * 7. 验证必填字段是否保存成功
     */
    test('添加必填字段应用', async () => {
      // 添加只包含必填字段的应用
      await applicationPage.add(testData.requiredFieldsData);

      // 搜索并验证应用信息
      await applicationPage.search({ applicationName: testData.requiredFieldsData.applicationName });
      applicationId = await applicationPage.getCellValue('应用ID');

      // 定位到目标 div 并验证其可见性
      await applicationPage.viewDetail({ applicationName: testData.requiredFieldsData.applicationName });
      const viewModalDiv = applicationPage.locator('div.row.view-modal');
      await expect(viewModalDiv).toBeVisible();

      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.requiredFieldsData.applicationName);
      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.requiredFieldsData.clientId);
      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.requiredFieldsData.channel);
    });

    /**
     * 测试用例：新增应用
     * 步骤：
     * 1. 打开应用管理页面
     * 2. 点击新增按钮
     * 3. 填写所有应用字段
     * 4. 点击保存按钮
     * 5. 查看应用详情
     * 6. 验证所有字段是否保存成功
     */
    test('新增应用', async () => {
      // 添加基础应用
      await applicationPage.add(testData.basicApplicationData);

      // 定位到目标 div 并验证其可见性
      await applicationPage.viewDetail({ applicationName: testData.basicApplicationData.applicationName });
      const viewModalDiv = applicationPage.locator('div.row.view-modal');
      await expect(viewModalDiv).toBeVisible();

      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.basicApplicationData.applicationName);
      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.basicApplicationData.shortName || '');
      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.basicApplicationData.category || '');
      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.basicApplicationData.clientId);
      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.basicApplicationData.domain || '');
      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.basicApplicationData.channel);
      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.basicApplicationData.remark || '');
    });




    /**
     * 测试用例：编辑应用
     * 步骤：
     * 1. 打开应用管理页面
     * 2. 搜索要编辑的应用
     * 3. 点击编辑按钮
     * 4. 修改应用信息
     * 5. 点击保存按钮
     * 6. 查看应用详情
     * 7. 验证所有字段是否修改成功
     */
    test('编辑应用', async () => {
      // 编辑应用
      await applicationPage.edit({ applicationName: testData.basicApplicationData.applicationName }, testData.editApplicationData);

      // 定位到目标 div 并验证其可见性
      await applicationPage.viewDetail({ applicationName: testData.editApplicationData.applicationName });
      const viewModalDiv = applicationPage.locator('div.row.view-modal');
      await expect(viewModalDiv).toBeVisible();

      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.editApplicationData.applicationName);
      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.editApplicationData.shortName || '');
      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.editApplicationData.category || '');
      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.editApplicationData.clientId);
      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.editApplicationData.domain || '');
      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.editApplicationData.channel);
      await expect(applicationPage.locator('app-application-management-detail')).toContainText(testData.editApplicationData.remark || '');
    });

    /**
     * 测试用例：验证搜索功能
     * 步骤：
     * 1. 打开应用管理页面
     * 2. 使用多个条件进行组合搜索（应用ID、应用名称、简称、应用分类、备注）
     * 3. 点击查询按钮
     * 4. 验证搜索结果是否符合预期
     * 5. 清空搜索条件
     */
    test('验证搜索功能', async () => {
      // 使用综合条件进行搜索
      await applicationPage.search({
        applicationId: applicationId,
        applicationName: testData.editApplicationData.applicationName,
        shortName: testData.editApplicationData.shortName,
        category: testData.editApplicationData.category,
        remark: testData.editApplicationData.remark
      });

      // 验证搜索结果
      await expect(applicationPage.locator('.datatable-row-center.datatable-row-group').first()).toBeVisible();

      // 清空搜索条件
      await applicationPage.clearSearch();
    });

    /**
     * 测试用例：删除应用
     * 步骤：
     * 1. 打开应用管理页面
     * 2. 搜索要删除的应用
     * 3. 点击删除按钮
     * 4. 确认删除操作
     * 5. 搜索并验证应用已删除
     * 6. 重复上述步骤删除所有测试应用
     */
    test('删除应用', async () => {
      // 删除应用
      await applicationPage.delete({ applicationName: testData.editApplicationData.applicationName });
      await applicationPage.delete({ applicationName: testData.requiredFieldsData.applicationName });

      // 搜索并验证应用已删除
      await applicationPage.search({ applicationName: testData.editApplicationData.applicationName });
      await expect(applicationPage.locator('.datatable-row-center.datatable-row-group')).not.toBeVisible();

      await applicationPage.search({ applicationName: testData.requiredFieldsData.applicationName });
      await expect(applicationPage.locator('.datatable-row-center.datatable-row-group')).not.toBeVisible();
    });
  });
});