import { Page, Browser, BrowserContext } from '@playwright/test';
import * as path from 'path';

/**
 * 创建浏览器上下文
 * @param browser 浏览器实例
 * @param storageState 存储状态文件路径，用于恢复登录状态
 * @returns 浏览器上下文实例
 */
async function createContext(browser: Browser, storageState: string): Promise<BrowserContext> {
  return await browser.newContext({
    storageState: storageState
  });
}

/**
 * 浏览器管理器类
 * 使用单例模式管理浏览器上下文和页面实例
 * 提供统一的接口来创建和管理浏览器页面
 */
export class BrowserManager {
  private static instance: BrowserManager;
  private contexts: Map<string, BrowserContext> = new Map();
  private pages: Map<string, Page> = new Map();
  private isCleaningUp: boolean = false;
  private defaultBrowser: Browser | null = null;

  private constructor() {
    // 注册进程退出时的清理处理
    process.on('SIGINT', this.handleProcessExit.bind(this));
    process.on('SIGTERM', this.handleProcessExit.bind(this));
    process.on('uncaughtException', this.handleProcessExit.bind(this));
    process.on('unhandledRejection', this.handleProcessExit.bind(this));
  }

  /**
   * 设置默认浏览器实例
   * @param browser 浏览器实例
   */
  public setDefaultBrowser(browser: Browser) {
    this.defaultBrowser = browser;
  }

  /**
   * 获取默认浏览器实例
   * @returns 浏览器实例
   * @throws Error 如果默认浏览器未设置
   */
  private getDefaultBrowser(): Browser {
    if (!this.defaultBrowser) {
      throw new Error('默认浏览器未设置，请先调用 setDefaultBrowser');
    }
    return this.defaultBrowser;
  }

  /**
   * 处理进程退出事件
   * 确保在进程退出时清理所有资源
   */
  private async handleProcessExit() {
    if (this.isCleaningUp) return;
    this.isCleaningUp = true;
    
    try {
      await this.closeAll();
      console.log('已清理所有浏览器资源');
    } catch (error) {
      console.error('清理浏览器资源时出错:', error);
    } finally {
      this.isCleaningUp = false;
    }
  }

  /**
   * 获取BrowserManager的单例实例
   * @returns BrowserManager实例
   */
  public static getInstance(): BrowserManager {
    if (!BrowserManager.instance) {
      BrowserManager.instance = new BrowserManager();
    }
    return BrowserManager.instance;
  }

  /**
   * 获取或创建浏览器上下文
   * @param browser 浏览器实例
   * @param storageState 存储状态文件路径
   * @returns 浏览器上下文实例
   */
  public async getContext(browser: Browser, storageState: string): Promise<BrowserContext> {
    if (!this.contexts.has(storageState)) {
      const context = await createContext(browser, storageState);
      this.contexts.set(storageState, context);
    }
    return this.contexts.get(storageState)!;
  }

  /**
   * 获取或创建页面实例
   * @param context 浏览器上下文
   * @param name 页面名称
   * @returns 页面实例
   */
  public async getPage(context: BrowserContext, name: string): Promise<Page> {
    if (!this.pages.has(name)) {
      const page = await context.newPage();
      this.pages.set(name, page);
    }
    return this.pages.get(name)!;
  }

  /**
   * 创建页面实例并初始化对应的页面类
   * @param browser 浏览器实例
   * @param storageState 存储状态文件路径
   * @param PageClass 页面类构造函数
   * @returns 初始化后的页面实例
   * 
   * @example
   * // 使用默认页面名称（页面类名）
   * const page = await browserManager.createPage(browser, storageState, LoginPage);
   * 
   * // 自定义页面名称
   * const page = await browserManager.createPage(browser, storageState, LoginPage, 'custom-page');
   */
  public async createPage<T>(
    browser: Browser,
    storageState: string,
    PageClass: new (page: Page) => T,
    pageName?: string
  ): Promise<T> {
    const context = await this.getContext(browser, storageState);
    const pageInstance = await this.getPage(context, pageName || PageClass.name);
    return new PageClass(pageInstance);
  }

  /**
   * 关闭所有浏览器上下文和页面实例
   * 用于测试结束时的清理工作
   */
  public async closeAll() {
    if (this.isCleaningUp) return;
    this.isCleaningUp = true;
    
    try {
      // 先关闭所有页面
      for (const page of this.pages.values()) {
        await page.close();
      }
      this.pages.clear();

      // 然后关闭所有上下文
      for (const context of this.contexts.values()) {
        await context.close();
      }
      this.contexts.clear();
      console.log('已关闭所有浏览器上下文和页面');
    } catch (error) {
      console.error('关闭浏览器上下文时出错:', error);
      throw error;
    } finally {
      this.isCleaningUp = false;
    }
  }
} 