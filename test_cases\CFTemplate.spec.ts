import { test, expect } from '@playwright/test';
import { CFTemplatePage } from '../test_pages/CFTemplatePage';
import { getCurrentConfig } from '../config';
import { BrowserManager } from '../utils/utils';
import * as testData from '../test_datas/CFTemplateData';

const config = getCurrentConfig();

// 配置测试用例为串行执行，确保测试用例按顺序执行
test.describe.configure({ mode: 'serial' });

// 定义全局变量
let cfTemplatePage: CFTemplatePage;
let browserManager: BrowserManager;

test.describe('处方模板管理', () => {

  // 在所有测试用例执行前初始化浏览器管理器和页面对象
  test.beforeAll(async ({ browser }) => {
    // 初始化浏览器管理器
    browserManager = BrowserManager.getInstance();
    // 创建模板管理页面实例，使用医师权限
    cfTemplatePage = await browserManager.createPage(browser, config.STORAGE.DOCTOR_STORAGE, CFTemplatePage);
  });

  // 处方模板测试套件
  test.describe('ZYWZ-373 新增模板操作', () => {

    test('空数据保存', async () => {
      // 执行新增模板操作
      await cfTemplatePage.add(testData.emptyData);
      // 验证错误提示
      await expect(cfTemplatePage.getByRole('alertdialog')).toContainText('信息填写不完整，请检查');
      await cfTemplatePage.cancel();
    });

    test('处方详情为空保存', async () => {
      // 执行新增模板操作
      await cfTemplatePage.add(testData.detailEmptyData);
      // 验证错误提示
      await expect(cfTemplatePage.getByRole('alertdialog')).toContainText('信息填写不完整，请检查');
      await cfTemplatePage.cancel();
    });

    test('必填字段保存', async () => {
      // 执行新增模板操作
      await cfTemplatePage.add(testData.reqAddData);
      // 验证模板是否创建成功
      await expect(cfTemplatePage.locator('datatable-scroller')).toContainText(testData.reqAddData.name);
    });

    test('填写已存在的模板名称', async () => {
      // 执行新增模板操作
      await cfTemplatePage.add(testData.reqAddData);
      // 验证错误提示
      await expect(cfTemplatePage.getByRole('alertdialog')).toContainText('保存失败。模版名称重复，请检查');
      await cfTemplatePage.cancel();
    });

    test('完整字段保存', async () => {
      // 执行新增模板操作
      await cfTemplatePage.add(testData.fullAddData);
      // 验证模板是否创建成功
      await expect(cfTemplatePage.locator('datatable-scroller')).toContainText(testData.fullAddData.name);
    });
  });

  test.describe('ZYWZ-376 编辑模板操作', () => {

    test('清空模板内容保存', async () => {
      // 执行编辑模板操作
      await cfTemplatePage.edit({ name: testData.fullAddData.name }, testData.emptyData);
      // 验证模板是否编辑成功
      await expect(cfTemplatePage.getByRole('alertdialog')).toContainText('信息填写不完整，请检查');
      await cfTemplatePage.cancel();
    });

    test('清空处方详情内容保存', async () => {
      // 执行编辑模板操作
      await cfTemplatePage.edit({ name: testData.fullAddData.name }, testData.detailEmptyData);
      // 验证模板是否编辑成功
      await expect(cfTemplatePage.getByRole('alertdialog')).toContainText('信息填写不完整，请检查');
      await cfTemplatePage.cancel();
    });

    test('填写已存在的模板名称保存', async () => {
      // 执行新增模板操作
      await cfTemplatePage.edit({ name: testData.fullAddData.name }, testData.reqAddData);
      // 验证错误提示
      await expect(cfTemplatePage.getByRole('alertdialog')).toContainText('保存失败。模版名称重复，请检查');
      await cfTemplatePage.cancel();
    });

    test('修改必填字段内容保存', async () => {
      // 执行编辑模板操作
      await cfTemplatePage.edit({ name: testData.fullAddData.name }, testData.reqEditData);
      await cfTemplatePage.clearSearch()
      // 验证模板是否编辑成功
      await cfTemplatePage.detail({ name: testData.reqEditData.name });
      const detailDialog = cfTemplatePage.locator('.modal-dialog');
      await expect(detailDialog).toContainText(testData.reqEditData.name);
      await expect(detailDialog).toContainText(testData.reqEditData.type);
      await expect(detailDialog).toContainText(testData.reqEditData.diagnosis);

      await expect(detailDialog).toContainText(testData.fullAddData.decoction || '');
      await expect(detailDialog).toContainText(testData.fullAddData.totalDosage || '');
      await expect(detailDialog).toContainText(testData.fullAddData.usage || '');
      await expect(detailDialog).toContainText(testData.fullAddData.frequency || '');
      await expect(detailDialog).toContainText(testData.fullAddData.remark || '');
      await expect(detailDialog).toContainText(testData.fullAddData.medicines?.[0].name || '');
    });

    test('修改所有字段内容保存', async () => {
      // 执行编辑模板操作
      await cfTemplatePage.edit({ name: testData.reqEditData.name }, testData.fullEditData);
      // 验证模板是否编辑成功
      await cfTemplatePage.detail({ name: testData.fullEditData.name });

      // 验证模板详情页面内容
      const detailDialog = cfTemplatePage.locator('.modal-dialog');

      // 验证对话框中是否包含所有测试数据
      await expect(detailDialog).toContainText(testData.fullEditData.name);
      await expect(detailDialog).toContainText(testData.fullEditData.type);
      await expect(detailDialog).toContainText(testData.fullEditData.diagnosis);
      await expect(detailDialog).toContainText(testData.fullEditData.decoction || '');
      await expect(detailDialog).toContainText(testData.fullEditData.totalDosage || '');
      await expect(detailDialog).toContainText(testData.fullEditData.usage || '');
      await expect(detailDialog).toContainText(testData.fullEditData.frequency || '');
      await expect(detailDialog).toContainText(testData.fullEditData.remark || '');
      await expect(detailDialog).toContainText(testData.fullEditData.medicines?.[0].name || '');
    });
  });
});
