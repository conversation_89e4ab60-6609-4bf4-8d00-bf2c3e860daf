# Playwright TCMSP 自动化测试框架

## 框架介绍
这是一个基于 Playwright 的自动化测试框架，使用 TypeScript 语言编写，用于 TCMSP 系统的UI自动化测试。框架支持多环境（FAT/UAT/PROD）测试。

## 目录结构
```
playwright-tcmsp/
├── auth-data/          # 认证数据存储目录
├── test_cases/         # 测试用例目录
│   ├── BATemplate.spec.ts    # 病案模板测试用例
│   ├── CFTemplate.spec.ts    # 处方模板测试用例
│   ├── Medicine.spec.ts      # 药品管理测试用例
│   ├── Role.spec.ts          # 角色管理测试用例
│   └── Application.spec.ts   # 应用管理测试用例
├── test_pages/         # 页面对象模型目录
│   ├── BATemplatePage.ts     # 病案模板页面对象
│   ├── CFTemplatePage.ts     # 处方模板页面对象
│   ├── MedicinePage.ts       # 药品管理页面对象
│   ├── RolePage.ts           # 角色管理页面对象
│   └── ApplicationPage.ts    # 应用管理页面对象
├── test_datas/         # 测试数据目录
│   ├── BATemplateData.ts     # 病案模板测试数据
│   ├── CFTemplateData.ts     # 处方模板测试数据
│   ├── MedicineData.ts       # 药品管理测试数据
│   ├── RoleData.ts           # 角色管理测试数据
│   └── ApplicationData.ts    # 应用管理测试数据
├── utils/              # 工具类目录
│   ├── utils.ts             # 通用工具函数
│   └── browserManager.ts    # 浏览器管理工具
├── config.ts           # 配置文件
├── global-setup.ts     # 全局设置
├── playwright.config.ts # Playwright 配置
└── package.json        # 项目依赖配置
```

## 安装方法

### 前置要求
- Node.js (建议使用 LTS 版本)
- Git
- Chrome 浏览器

### 安装步骤
1. 克隆项目
```bash
git clone [项目地址]
cd playwright-tcmsp
```

2. 安装依赖
```bash
npm install
```

3. 安装 Playwright 浏览器
```bash
npx playwright install
```

4. 配置环境变量
- 设置 `ENVIRONMENT` 环境变量（FAT/UAT/PROD）
- 可以通过命令行或系统环境变量设置

## 环境变量说明

### 环境配置
- `ENVIRONMENT`: 指定测试环境，可选值为 'FAT'、'UAT'、'PROD'，默认为 'FAT'

### 系统配置
在 `config.ts` 文件中定义了各环境的配置信息：
```typescript
export const URLS = {
  FAT: {
    TCMSP_URL: 'http://fat.example.com/',
  },
  UAT: {
    TCMSP_URL: 'http://uat.example.com/',
  },
  PROD: {
    TCMSP_URL: 'http://prod.example.com/',
  }
};

export const ACCOUNTS = {
  FAT: {
    ADMIN: { username: 'admin', password: 'admin123' },
    DOCTOR: { username: 'doctor', password: 'doctor123' },
    INSTITUTION: { username: 'institution', password: 'institution123' }
  },
  // UAT 和 PROD 环境的账号配置...
};

export const STORAGE = {
  ADMIN_STORAGE: './auth-data/admin-storage.json',
  DOCTOR_STORAGE: './auth-data/doctor-storage.json',
  INSTITUTION_STORAGE: './auth-data/institution-storage.json'
};
```

## 通用方法说明

### BrowserManager 类
`BrowserManager` 是一个单例类，用于管理浏览器实例和页面对象：

```typescript
// 获取实例
const browserManager = BrowserManager.getInstance();

// 创建页面对象
const page = await browserManager.createPage(browser, config.STORAGE.ADMIN_STORAGE, PageClass);
```

### 页面对象通用方法
所有页面对象都应该实现以下通用方法：

- `open()`: 打开页面
- `add(data)`: 新增数据
- `edit(searchParams, formParams)`: 编辑数据
- `delete(searchParams)`: 删除数据
- `search(params)`: 搜索数据
- `confirm()`: 确认操作
- `cancel()`: 取消操作
- `close()`: 关闭对话框

## 测试用例编写规范

### 1. 页面对象编写规范 (test_pages)

#### 文件命名
- 使用 PascalCase 命名法
- 以 `Page.ts` 结尾，例如 `RolePage.ts`

#### 类结构
```typescript
import { Page } from '@playwright/test';
import { getCurrentConfig } from '../config';
import { FormParams, SearchParams } from '../test_datas/DataFile';

const config = getCurrentConfig();

export class ModulePage {
  constructor(private page: Page) {}

  // 必须实现的方法
  async open() {
    await this.page.goto(config.URLS.TCMSP_URL + 'modulePath');
  }

  async add(data: FormParams) {
    await this.open();
    await this.page.getByText('新 增').click();
    await this.fillForm(data);
    await this.confirm();
  }

  async confirm() {
    await this.page.getByRole('button', { name: '保 存' }).click();
  }

  async cancel() {
    await this.page.getByRole('button', { name: '取 消' }).click();
  }

  async close() {
    await this.page.getByRole('button', { name: '关 闭' }).click();
  }

  async edit(searchParams: SearchParams, formParams: FormParams) {
    await this.search(searchParams);
    await this.page.getByText('编辑').first().click();
    await this.fillForm(formParams);
    await this.confirm();
  }

  async delete(searchParams: SearchParams) {
    await this.search(searchParams);
    await this.page.getByText('删除').first().click();
    await this.page.getByRole('button', { name: '确 定' }).click();
  }

  async search(params: SearchParams = {}) {
    await this.open();
    // 清空搜索条件
    await this.clearSearch();
    // 填写搜索条件
    // ...
    // 点击查询按钮
    await this.page.getByRole('button', { name: '查询' }).click();
  }

  async clearSearch() {
    await this.page.getByRole('button', { name: '清空' }).click();
  }

  async fillForm(data: FormParams) {
    // 实现表单填写逻辑
  }

  // 辅助方法
  async getCellValue(columnName: string, rowIndex: number = 0): Promise<string> {
    // 获取表格单元格值的实现
  }

  // 代理方法
  locator(selector: string) {
    return this.page.locator(selector);
  }

  getByRole(role: string, options?: { name?: string | RegExp }) {
    return this.page.getByRole(role, options);
  }

  getByText(text: string | RegExp, options?: { exact?: boolean }) {
    return this.page.getByText(text, options);
  }
}
```

#### 元素定位方法优先级
1. 使用 Playwright 推荐的语义化定位方法：
   - `getByRole()`: 基于元素角色定位
   - `getByText()`: 基于文本内容定位
   - `getByPlaceholder()`: 基于占位符定位
   - `getByLabel()`: 基于标签定位
2. 使用 CSS 选择器：
   - `.class-name:has-text("文本")`: 基于类名和文本内容定位
   - `[attribute=value]`: 基于属性定位
3. 避免使用 XPath 选择器，除非必要

### 2. 测试数据编写规范 (test_datas)

#### 文件命名
- 使用 PascalCase 命名法
- 以 `Data.ts` 结尾，例如 `RoleData.ts`

#### 数据结构
```typescript
/**
 * 表单参数接口
 */
export interface FormParams {
  /** 字段1，必填 */
  field1: string;
  /** 字段2，必填 */
  field2: string;
  /** 字段3，非必填 */
  field3?: string;
}

/**
 * 搜索参数接口
 */
export interface SearchParams {
  /** 搜索字段1 */
  searchField1?: string;
  /** 搜索字段2 */
  searchField2?: string;
}

// 生成时间戳标记，用于创建唯一的测试数据
const tag = Date.now();

// 空数据
export const emptyData: FormParams = {
  field1: '',
  field2: ''
};

// 必填字段测试数据
export const requiredFieldsData: FormParams = {
  field1: `必填字段1${tag}`,
  field2: `必填字段2${tag}`
};

// 完整字段测试数据
export const completeData: FormParams = {
  field1: `完整字段1${tag}`,
  field2: `完整字段2${tag}`,
  field3: `完整字段3${tag}`
};

// 编辑测试数据
export const editData: FormParams = {
  field1: `编辑字段1${tag}`,
  field2: `编辑字段2${tag}`,
  field3: `编辑字段3${tag}`
};

// 搜索测试数据
export const searchData: SearchParams = {
  searchField1: '搜索条件1',
  searchField2: '搜索条件2'
};
```

#### 数据定义规范
1. 使用 TypeScript 接口定义数据结构
2. 为每个字段添加注释，标明是否必填
3. 使用时间戳创建唯一的测试数据名称
4. 提供多种测试场景的数据：空数据、必填字段数据、完整字段数据、编辑数据等
5. 搜索参数单独定义接口

### 3. 测试用例编写规范 (test_cases)

#### 文件命名
- 使用 PascalCase 命名法
- 以 `.spec.ts` 结尾，例如 `Role.spec.ts`

#### 测试用例结构
```typescript
import { test, expect } from '@playwright/test';
import { ModulePage } from '../test_pages/ModulePage';
import { getCurrentConfig } from '../config';
import { BrowserManager } from '../utils/utils';
import * as testData from '../test_datas/ModuleData';

const config = getCurrentConfig();

// 配置测试用例为串行执行，确保测试用例按顺序执行
test.describe.configure({ mode: 'serial' });

test.describe('模块名称', () => {
  // 定义全局变量
  let modulePage: ModulePage;
  let browserManager: BrowserManager;

  test.beforeAll(async ({ browser }) => {
    // 初始化浏览器管理器
    browserManager = BrowserManager.getInstance();
    // 创建页面实例，使用适当的权限
    modulePage = await browserManager.createPage(browser, config.STORAGE.ADMIN_STORAGE, ModulePage);
  });

  test.describe('ZYWZ-XXX 功能描述', () => {
    /**
     * 测试用例：空数据保存
     * 步骤：
     * 1. 打开模块页面
     * 2. 点击新增按钮
     * 3. 不填写任何数据
     * 4. 点击保存按钮
     * 5. 验证错误提示
     */
    test('空数据保存', async () => {
      // 执行新增操作
      await modulePage.add(testData.emptyData);
      // 验证错误提示
      await expect(modulePage.getByRole('alertdialog')).toContainText('表单数据不完整，请检查');
      await modulePage.cancel();
    });

    /**
     * 测试用例：添加必填字段数据
     * 步骤：
     * 1. 打开模块页面
     * 2. 点击新增按钮
     * 3. 填写必填字段
     * 4. 点击保存按钮
     * 5. 验证数据是否创建成功
     */
    test('添加必填字段数据', async () => {
      // 执行新增操作
      await modulePage.add(testData.requiredFieldsData);
      // 验证是否创建成功
      await modulePage.search({ field1: testData.requiredFieldsData.field1 });
      await expect(modulePage.locator('datatable-body-row')).toBeVisible();
      await expect(modulePage.locator('datatable-body-row')).toContainText(testData.requiredFieldsData.field1);
    });

    // 更多测试用例...
  });
});

#### 测试用例编写规范
1. 每个测试用例前必须添加详细的步骤注释，格式如下：
   ```typescript
   /**
    * 测试用例：用例名称
    * 步骤：
    * 1. 步骤1
    * 2. 步骤2
    * ...
    */
   ```
2. 测试用例顺序：
   - 先执行"空数据保存"测试
   - 再执行"必填字段"测试
   - 然后执行"完整字段"测试
   - 最后执行"编辑"和"删除"测试
3. 使用 `test.describe.configure({ mode: 'serial' })` 确保测试用例按顺序执行
4. 在 `beforeAll` 中初始化页面对象，使用适当的权限
5. 使用 `expect` 进行断言，验证测试结果

## 运行测试

### 运行所有测试
```bash
npx playwright test
```

### 运行特定模块测试
```bash
npx playwright test --grep '模块名称'
```

### 运行测试并显示浏览器
```bash
npx playwright test --headed
```

### 调试测试
```bash
npx playwright test --debug
```

## 常见问题

1. 如果遇到浏览器启动问题，尝试重新安装 Playwright 浏览器
2. 如果遇到认证问题，检查 `auth-data` 目录下的认证文件
3. 如果遇到网络问题，检查代理设置和网络连接

## 技术支持
如有问题，请联系项目维护人员或提交 Issue。