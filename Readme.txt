# 切换环境
npm run test:fat
npm run test:uat
npm run test:prod

# 执行单个测试文件
npx playwright test XXX.spec.ts
npm run test:fat -- -- XXX.spec.ts

# 执行包含特定名称的测试套件
npx playwright test -grep "XXX"
npm run test:fat -- --grep "XXX"

# 执行带有特定标签的测试
npx playwright test --grep @smoke
npx playwright test --grep @regression
npm run test:fat -- --g @smoke

# 为什么是两个 -- ？
第一个 -- 起分隔作用，避免程序认为 --g 是给npm的参数
具体命令行参照package.json


要打开浏览器界面 后面加个 --headed

示例：按顺序执行测试用例，失败不重试
npx playwright test --headed --workers=1 --retries=0 --max-failures=1 --project=chromium
npm run test:fat -- --headed --workers=1 --retries=0 --max-failures=1 --project=chromium